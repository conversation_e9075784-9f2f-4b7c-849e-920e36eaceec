# 指定清华 CRAN 镜像
cran_repo <- "https://mirrors.nwafu.edu.cn/cran/"

# 定义函数
ensure_package <- function(pkg, repo = cran_repo) {
  if (!suppressMessages(require(pkg, character.only = TRUE, quietly = TRUE))) {
    install.packages(pkg, repos = repo)
    suppressMessages(library(pkg, character.only = TRUE))
  } else {
    suppressMessages(library(pkg, character.only = TRUE))
  }
}

# 使用示例
package_list <- c("GetoptLong", "tidyverse", "reshape2","vegan","ape")
for (p in package_list) {
  ensure_package(p)
}

rm(list = ls(all.names = TRUE))

# 参数定义，注意 Treemap 没有加 []，确保是合法变量名
GetoptLong(
  "Source=s", "Total abundance table, e.g., kraken2_taxonomic_profiles.tsv",
  "Group=s", "group table, e.g., group.txt",
  "Abundance=s", "Output dir for Total Relative Abundance Table",
  "Barplot=s", "Output dir for Barplot Table",
  "Heatmap=s", "Output dir for Heatmap Table",
  "PCoA=s", "Output dir for PCoA Table",
  "Treemap=s", "Output dir for Treemap Table (optional)",
  "SelectDomain=s", "Specify one domain, e.g., B",
  "SelectTaxonomy=s", "Specify one taxonomy level, e.g., G",
  "verbose!", "print messages"
)

########################## 自定义函数 ##########################

ra_cal = function(data = taxonomic_profiles,
                  domain_level = 'B',
                  tax_level = 'P'){
  tax_df = data %>%
    {.[.$Domain %in% domain_level,]} %>%
    {.[.$Level %in% tax_level,]}
  rownames(tax_df) = tax_df$Clade_name

  tax_df.t = tax_df %>%
    {subset(., select=-c(get('Domain')))} %>%
    {subset(., select=-c(get('Level')))} %>%
    {subset(., select=-c(get('Clade_name')))} %>%
    {.[rowSums(.)>0,]} %>%
    {.[,colSums(.)>0]} %>%
    {as.data.frame(t(.))} %>%
    {./rowSums(.)}
  return(tax_df.t)
}

tax_cal = function(data = tax_df.t,
                   tax_num = 29,
                   digit_num = 6){
  if (dim(data)[2] < tax_num) {
    need_data = data %>%
      {.[, order(colSums(.),decreasing = TRUE)]}
  } else {
    need_data = data %>%
      {.[, order(colSums(.),decreasing = TRUE)]} %>%
      {.[,1:tax_num]}
  }
  Others = 1 - rowSums(need_data)
  need_data = cbind(need_data, Others) %>%
    {(round((. * 100), digit_num))}
  return(need_data)
}

merge_cal = function(data = need_data, group = group){
  data$sample_name <- rownames(data)
  data_merge = merge(data, group, by = 'sample_name', all.x = TRUE)
  rownames(data_merge) = data_merge$sample_name
  levels_group = sort(unique(group$group))
  data_merge$group <- factor(data_merge$group, levels = levels_group)
  return(data_merge)
}

melt_cal = function(data = need_data, group = group,
                    variable.name='Taxonomy', value.name='Abudance'){
  colname_data = colnames(data)
  plotdata_group = merge_cal(data, group) %>%
    melt(id.vars = c('sample_name','group'),
         measure.vars = colname_data,
         variable.name = variable.name,
         value.name = value.name)
  return(plotdata_group)
}

########################## 数据读取 ##########################

taxonomic_profiles <- read.csv(Source, sep = '\t', quote = '')
group = read.csv(Group, sep = '\t', header = TRUE, row.names = 1)
levels_group = sort(unique(group$group))
group$group = factor(group$group, levels = levels_group)
group$sample_name <- rownames(group)

########################## 创建输出目录 ##########################

out_dirs = c(Abundance, Barplot, Heatmap, PCoA)
if (!missing(Treemap)) {
  out_dirs = c(out_dirs, Treemap)
}

for (dir in out_dirs) {
  if (!dir.exists(dir)) dir.create(dir, recursive = TRUE)
}

########################## 主逻辑：一个Domain + 一个分类 ##########################

domain <- SelectDomain
taxonomy <- SelectTaxonomy

cat("Running Domain:", domain, "| Taxonomy Level:", taxonomy, "\n")

# 相对丰度表
ra_tax = ra_cal(taxonomic_profiles, domain, taxonomy)
ra_tax %>%
  {(round((. * 100), 6))} %>%
  {as.data.frame(t(.))} %>%
  {rownames_to_column(., var = 'taxon')} %>%
  {write.table(.,
               paste0(Abundance, "/", domain, ".", taxonomy, ".percent.csv"),
               sep = ",", row.names = FALSE, quote = FALSE, fileEncoding = "UTF-8")}

# Barplot 数据
barplot_table = tax_cal(ra_tax, 29, 4)
data.frame(Tax = colnames(barplot_table)) %>%
  {write.table(.,
               paste0(Barplot, "/", "Barlegend.", domain, ".", taxonomy, ".csv"),
               sep = ",", row.names = FALSE, quote = FALSE, fileEncoding = "UTF-8")}

barplot_table %>%
  melt_cal(group = group, variable.name = 'Taxonomy', value.name = 'Abudance') %>%
  {write.table(.,
               paste0(Barplot, "/", "Barplot.", domain, ".", taxonomy, ".csv"),
               sep = ",", row.names = FALSE, quote = FALSE, fileEncoding = "UTF-8")}

# 距离矩阵 + PCoA（种水平）
for (beta in c('bray', 'jaccard')) {
  cat("Computing distance (", beta, ")...\n")
  dist_matrix = ra_cal(taxonomic_profiles, domain, 'S') %>%
    vegdist(method = beta, na.rm = TRUE) %>%
    as.matrix(as.dist(.))

  dist_matrix %>%
    {round(., 6)} %>%
    {as.data.frame(.)} %>%
    {rownames_to_column(., var = 'sample_name')} %>%
    {write.table(.,
                 paste0(Heatmap, "/", "Heatmap.", domain, ".", beta, ".csv"),
                 sep = ",", row.names = FALSE, quote = FALSE, fileEncoding = "UTF-8")}

  df.pcoa = pcoa(dist_matrix, correction = "cailliez")

  data.frame(
    x_label = round(df.pcoa$values$Rel_corr_eig[1] * 100, 1),
    y_label = round(df.pcoa$values$Rel_corr_eig[2] * 100, 1)
  ) %>%
    {write.table(.,
                 paste0(PCoA, "/", "PCoA.label.", domain, ".", taxonomy, ".",beta, ".csv"),
                 sep = ",", row.names = FALSE, quote = FALSE, fileEncoding = "UTF-8")}

  df.pcoa$vectors %>%
    as.data.frame() %>%
    merge_cal(group) %>%
    {.[colnames(.) %in% c('sample_name', 'group', 'Axis.1', 'Axis.2')]} %>%
    {write.table(.,
                 paste0(PCoA, "/", "PCoA.", domain, ".",taxonomy, ".", beta, ".csv"),
                 sep = ",", row.names = FALSE, quote = FALSE, fileEncoding = "UTF-8")}
}

########################## Treemap 表格输出 ##########################

if (!missing(Treemap)) {
  cat("Generating Treemap Table for Domain:", domain, "and Taxonomy Level:", taxonomy, "\n")
  treemap_df = taxonomic_profiles %>%
    filter(Domain == domain, Level == taxonomy) %>%
    select(-Domain, -Level)

  # 将 Clade_name 作为行名，且列名顺序：Clade_name (taxon), samples...
  rownames(treemap_df) = treemap_df$Clade_name
  treemap_df = treemap_df %>%
    select(-Clade_name)

  # 保存时，行名作为第一列，列名保持样本名（注意此时第一列是taxon）
  treemap_out = as.data.frame(treemap_df)
  treemap_out = tibble::rownames_to_column(treemap_out, var = "Taxon")

  write.table(treemap_out,
              file = paste0(Treemap, "/", "Treemap.",domain, ".", taxonomy, ".csv"),
              sep = ",", row.names = FALSE, quote = FALSE, fileEncoding = "UTF-8")
}
