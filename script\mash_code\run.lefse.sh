#! /bin/bash
scripts_path=$1
inputPath=$2
outputPath=$3
Domain=$4
Level=$5




#准备lefse的输入文件
perl ${scripts_path}/Diversity/step6-Kraken_taxonomy2Community_V2.pl ${outputPath}/kraken2_taxonomic_profiles.tsv  ${inputPath}/Group.txt ${outputPath}/Lefse_Table ${Domain}  ${Level}  ${inputPath}/Group.txt

#将文件转为lefse的输入格式
sh ${scripts_path}/Diversity/step7-lefse_format.sh ${Domain}  ${Level}  ${inputPath}/metadata.tsv  ${outputPath}/Lefse_Table


#lefse分析,更换为lefse镜像
sh ${scripts_path}/Diversity/step8-differential.sh ${Domain}  ${Level} ${inputPath}/metadata.tsv ${outputPath}/Lefse_Table


#lefse柱形图绘制,更换为mash镜像
Rscript ${scripts_path}/Diversity/step9-kraken.Barplot_lefse.cmd.R --Group ${inputPath}/Group.txt --Group_color ${inputPath}/group_color.tsv --Input ${outputPath}/Lefse_Table  --Output ${outputPath}/Lefse_Figure --Domain ${Domain} --Level ${Level}  --verbose







