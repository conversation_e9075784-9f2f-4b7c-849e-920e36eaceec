# 指定清华 CRAN 镜像
cran_repo <- "https://mirrors.nwafu.edu.cn/cran/"

# 定义函数
ensure_package <- function(pkg, repo = cran_repo) {
  if (!suppressMessages(require(pkg, character.only = TRUE, quietly = TRUE))) {
    install.packages(pkg, repos = repo)
    suppressMessages(library(pkg, character.only = TRUE))
  } else {
    suppressMessages(library(pkg, character.only = TRUE))
  }
}

# 使用示例
package_list <- c("GetoptLong", "ggplot2", "tidyverse","plotly","reshape2")
for (p in package_list) {
  ensure_package(p)
}

rm(list = ls(all.names = TRUE))

# 解析命令行参数
GetoptLong(
  "Group=s", "Group table, two columns: sample_name and group",
  "Group_color=s", "Group color table, two columns: Group and Color",
  "Input=s", "Input directory containing LEfSe result files",
  "Output=s", "Output directory for plots",
  "Domain=s", "Domain (e.g., A, B, E, V)",
  "Level=s", "Taxonomic level short initial (P, C, O, F, G, S)",
  "verbose!", "Print messages"
)

# 映射首字母到完整 taxonomic level 名称（用于图标题）
level_map <- c(P = "phylum", C = "class", O = "order",
               F = "family", G = "genus", S = "species")

if (!(Level %in% names(level_map))) {
  stop("Invalid Level. Please use one of: P, C, O, F, G, S")
}
full_level <- level_map[Level]

# 创建绘图函数（保持原程序结构）
plot_graph <- function(file_LDA, levels_group, Group_color_each_map, levels_OTU) {

  if (length(levels_group) == 2) {
    file_LDA <- file_LDA %>%
      mutate(LDA_score = ifelse(group %in% levels_group[2], -1 * LDA_score, LDA_score))

    plot_LDA = file_LDA %>%
      mutate(Group = factor(group, levels = levels_group)) %>%
      arrange(Group, desc(LDA_score)) %>%
      mutate(OTU = factor(OTU, levels = rev(unique(OTU))))

    p1 = ggplot(plot_LDA, aes(x = OTU, y = LDA_score, fill = Group)) +
      geom_bar(stat = 'identity', width = 0.6) +
      coord_flip() +
      geom_text(data = subset(plot_LDA, LDA_score < 0),
                aes(x = OTU, y = 0, label = paste0(" ", OTU)),
                size = 5, color = 'black', hjust = "bottom") +
      geom_text(data = subset(plot_LDA, LDA_score > 0),
                aes(x = OTU, y = 0, label = paste0(OTU, " ")),
                size = 5, color = 'black', hjust = "inward", angle = 360) +
      scale_fill_manual(values = Group_color_each_map$Color) +
      scale_colour_manual(values = Group_color_each_map$Color) +
      theme(
        plot.title = element_text(hjust = 0.5),
        title = element_text(family = 'serif', size = 20),
        axis.text.x = element_text(size = (15 - levels_OTU / 20)),
        axis.text.y = element_blank(),
        axis.ticks = element_blank(),
        legend.title = element_blank(),
        legend.text = element_text(family = 'serif', size = 15),
        panel.background = element_rect(fill = NA),
        panel.grid.major.x = element_line(colour = "grey", linetype = "dashed"),
        axis.title.y = element_text(margin = margin(0, 0, 0, 0))
      ) +
      labs(x = NULL, y = 'LDA Score (log 10)')

  } else {
    plot_LDA = file_LDA %>%
      mutate(Group = factor(group, levels = levels_group)) %>%
      arrange(Group, desc(LDA_score)) %>%
      mutate(OTU = factor(OTU, levels = rev(unique(OTU))))

    p1 = ggplot(plot_LDA, aes(x = OTU, y = LDA_score, fill = Group)) +
      geom_bar(stat = 'identity', width = 0.6) +
      coord_flip() +
      scale_fill_manual(values = Group_color_each_map$Color) +
      scale_colour_manual(values = Group_color_each_map$Color) +
      theme(
        plot.title = element_text(hjust = 0),
        title = element_text(family = 'serif', size = 20),
        axis.text.x = element_text(size = (15 - levels_OTU / 20)),
        axis.text.y = element_text(hjust = 0, size = (15 - levels_OTU / 20)),
        axis.ticks = element_blank(),
        legend.title = element_blank(),
        legend.text = element_text(family = 'serif', size = 15),
        panel.background = element_rect(fill = NA),
        panel.grid.major.x = element_line(colour = "grey", linetype = "dashed"),
        axis.title.y = element_text(margin = margin(0, 0, 0, 0))
      ) +
      labs(x = NULL, y = 'LDA Score (log 10)')
  }

  return(p1)
}

########################## 数据读取 ##########################
group = read.csv(Group, sep = '\t', header = TRUE, row.names = 1, check.names = FALSE)
group$sample_name <- rownames(group)
levels_group = sort(unique(group$group))
group$group = factor(group$group, levels = levels_group)

Group_color_data = read.csv(Group_color, sep = '\t', header = TRUE, col.names = c('Group', 'Color'),
                            comment.char = "", stringsAsFactors = FALSE, check.names = FALSE) %>%
  filter(Group %in% levels_group)
rownames(Group_color_data) = Group_color_data$Group

if (!dir.exists(Output)) {
  dir.create(Output, recursive = TRUE)
}

# 指定 LDA 结果文件路径
LDA_file_name = file.path(Input, paste0("lefse.",Domain,".",Level, ".res"))

if (!file.exists(LDA_file_name)) {
  stop(paste("Lefse result file not found:", LDA_file_name))
}

file_LDA = read.csv(LDA_file_name, sep = '\t', header = FALSE, stringsAsFactors = FALSE)[, c(1, 3, 4)]
colnames(file_LDA) = c('OTU', 'group', 'LDA_score')
file_LDA$LDA_score = as.numeric(file_LDA$LDA_score)

file_LDA = file_LDA %>%
  filter(!is.na(LDA_score), !is.na(group)) %>%
  group_by(group) %>%
  top_n(10, LDA_score) %>%
  ungroup()

levels_OTU = length(file_LDA$OTU)

# 提取 LDA 中实际出现的 group
levels_group = group %>%
  filter(group %in% unique(file_LDA$group)) %>%
  {sort(unique(.$group))}

Group_color_each_map = Group_color_data %>%
  filter(Group %in% levels_group) %>%
  {.[match(levels_group, rownames(.)),]} %>%
  mutate(group = Group)

plot_height = 2 + 0.12 * levels_OTU

if (length(levels_group) > 0) {
  p3 = plot_graph(file_LDA, levels_group, Group_color_each_map, levels_OTU)
  out_file <- paste0("Lefse.", Domain, ".", Level, ".pdf")
  ggsave(
    filename = out_file,
    plot = p3,
    path = Output,
    width = 10,
    height = plot_height,
    limitsize = FALSE
  )
  if (verbose) {
    print(paste("Plot saved to:", file.path(Output, out_file)))
  }
}

